<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Check Console</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .console-output {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Dynamic Form Debug Test</h1>
        
        <div class="debug-section">
            <h2>Debug Functions Test</h2>
            <p>Open your browser's developer console (F12) and run these commands:</p>
            
            <button onclick="testDebugFunctions()">Test Debug Functions</button>
            <button onclick="testGridCalculation()">Test Grid Calculation</button>
            <button onclick="checkScreenSize()">Check Screen Size</button>
            
            <div class="console-output" id="output">
Console output will appear here...
            </div>
        </div>

        <div class="debug-section">
            <h2>Manual Console Commands</h2>
            <p>Copy and paste these commands into your browser console:</p>
            <ul>
                <li><code>window.debugGrid()</code> - Debug current grid state</li>
                <li><code>window.testRowSize()</code> - Test rowSize calculation</li>
                <li><code>window.debugDynamicForm</code> - Access component instance</li>
            </ul>
        </div>

        <div class="debug-section">
            <h2>Grid Area Test</h2>
            <p>Test the grid area calculation function:</p>
            <button onclick="testGridAreaCalculation()">Test Grid Area Calculation</button>
        </div>
    </div>

    <script>
        // Simulate the calculateGridArea function
        function calculateGridArea(field) {
            const row = field.row || 1;
            const column = field.column || 1;
            const rowSize = field.rowSize || 1;
            const colSize = field.colSize || 1;

            const rowStart = row;
            const rowEnd = row + rowSize;
            const colStart = column;
            const colEnd = column + colSize;

            return `${rowStart} / ${colStart} / ${rowEnd} / ${colEnd}`;
        }

        function updateOutput(message) {
            const output = document.getElementById('output');
            output.textContent += message + '\n';
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }

        function testDebugFunctions() {
            clearOutput();
            updateOutput('🔍 Testing Debug Functions...');
            
            // Check if debug functions are available
            if (typeof window.debugGrid === 'function') {
                updateOutput('✅ window.debugGrid() is available');
                try {
                    window.debugGrid();
                    updateOutput('✅ window.debugGrid() executed successfully');
                } catch (e) {
                    updateOutput('❌ Error calling window.debugGrid(): ' + e.message);
                }
            } else {
                updateOutput('❌ window.debugGrid() is NOT available');
            }

            if (typeof window.testRowSize === 'function') {
                updateOutput('✅ window.testRowSize() is available');
                try {
                    window.testRowSize();
                    updateOutput('✅ window.testRowSize() executed successfully');
                } catch (e) {
                    updateOutput('❌ Error calling window.testRowSize(): ' + e.message);
                }
            } else {
                updateOutput('❌ window.testRowSize() is NOT available');
            }

            if (window.debugDynamicForm) {
                updateOutput('✅ window.debugDynamicForm is available');
                updateOutput('Component type: ' + typeof window.debugDynamicForm);
            } else {
                updateOutput('❌ window.debugDynamicForm is NOT available');
            }
        }

        function testGridCalculation() {
            clearOutput();
            updateOutput('🧪 Testing Grid Area Calculation...');
            
            const testCases = [
                { fieldName: 'normal', row: 1, column: 1, rowSize: 1, colSize: 1, expected: '1 / 1 / 2 / 2' },
                { fieldName: 'rowSpan2', row: 1, column: 2, rowSize: 2, colSize: 1, expected: '1 / 2 / 3 / 3' },
                { fieldName: 'colSpan2', row: 2, column: 1, rowSize: 1, colSize: 2, expected: '2 / 1 / 3 / 3' },
                { fieldName: 'span2x2', row: 3, column: 1, rowSize: 2, colSize: 2, expected: '3 / 1 / 5 / 3' }
            ];

            testCases.forEach(testCase => {
                const result = calculateGridArea(testCase);
                const passed = result === testCase.expected;
                updateOutput(`${passed ? '✅' : '❌'} ${testCase.fieldName}: ${result} (expected: ${testCase.expected})`);
            });
        }

        function checkScreenSize() {
            clearOutput();
            updateOutput('📱 Screen Size Check...');
            updateOutput('Window width: ' + window.innerWidth + 'px');
            updateOutput('Window height: ' + window.innerHeight + 'px');
            updateOutput('Mobile breakpoint (≤768px): ' + (window.innerWidth <= 768 ? 'YES' : 'NO'));
            
            if (window.innerWidth <= 768) {
                updateOutput('⚠️ Mobile responsive override is likely ACTIVE');
                updateOutput('This will reset grid-area to "auto" and stack fields vertically');
                updateOutput('Try resizing window to >768px width to test rowSize');
            } else {
                updateOutput('✅ Desktop view - grid positioning should work normally');
            }
        }

        function testGridAreaCalculation() {
            clearOutput();
            updateOutput('🎯 Testing rowSize=2 specifically...');
            
            const testField = {
                fieldName: 'testRowSpan',
                row: 1,
                column: 1,
                rowSize: 2,
                colSize: 1
            };
            
            const result = calculateGridArea(testField);
            updateOutput('Input: ' + JSON.stringify(testField, null, 2));
            updateOutput('Output: ' + result);
            updateOutput('Expected: 1 / 1 / 3 / 2');
            updateOutput('Correct: ' + (result === '1 / 1 / 3 / 2' ? 'YES' : 'NO'));
            
            if (result === '1 / 1 / 3 / 2') {
                updateOutput('✅ Grid area calculation is working correctly');
                updateOutput('The issue is likely CSS-related (responsive overrides)');
            } else {
                updateOutput('❌ Grid area calculation has an issue');
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            updateOutput('🚀 Debug Test Page Loaded');
            updateOutput('Open Developer Console (F12) for full debug output');
            updateOutput('Click buttons above to run tests');
        };
    </script>
</body>
</html>

/**
 * Debug helper for grid positioning issues
 * This utility helps diagnose why rowSize might not be working as expected
 */

export class GridDebugHelper {
  
  /**
   * Debug grid area calculation
   */
  static debugGridArea(field: any): void {
    console.group(`🔍 Grid Area Debug for field: ${field.fieldName}`);
    
    const row = field.row || 1;
    const column = field.column || 1;
    const rowSize = field.rowSize || 1;
    const colSize = field.colSize || 1;

    console.log('📋 Input values:', {
      row,
      column,
      rowSize,
      colSize
    });

    const rowStart = row;
    const rowEnd = row + rowSize;
    const colStart = column;
    const colEnd = column + colSize;

    console.log('🧮 Calculations:', {
      rowStart,
      rowEnd,
      colStart,
      colEnd
    });

    const gridArea = `${rowStart} / ${colStart} / ${rowEnd} / ${colEnd}`;
    console.log('✅ Final grid-area:', gridArea);
    
    // Validate the calculation
    const expectedRowSpan = rowSize;
    const actualRowSpan = rowEnd - rowStart;
    const expectedColSpan = colSize;
    const actualColSpan = colEnd - colStart;
    
    console.log('🎯 Validation:', {
      expectedRowSpan,
      actualRowSpan,
      rowSpanCorrect: expectedRowSpan === actualRowSpan,
      expectedColSpan,
      actualColSpan,
      colSpanCorrect: expectedColSpan === actualColSpan
    });
    
    console.groupEnd();
    return gridArea;
  }

  /**
   * Debug CSS grid container
   */
  static debugGridContainer(element: HTMLElement): void {
    console.group('🏗️ Grid Container Debug');
    
    const computedStyle = window.getComputedStyle(element);
    
    console.log('📐 Grid Properties:', {
      display: computedStyle.display,
      gridTemplateColumns: computedStyle.gridTemplateColumns,
      gridTemplateRows: computedStyle.gridTemplateRows,
      gridAutoRows: computedStyle.gridAutoRows,
      gap: computedStyle.gap,
      gridGap: computedStyle.gridGap
    });
    
    console.log('📏 Container Dimensions:', {
      width: element.offsetWidth,
      height: element.offsetHeight,
      clientWidth: element.clientWidth,
      clientHeight: element.clientHeight
    });
    
    console.groupEnd();
  }

  /**
   * Debug grid field element
   */
  static debugGridField(element: HTMLElement, expectedGridArea: string): void {
    console.group(`🎯 Grid Field Debug: ${element.textContent?.substring(0, 20)}...`);
    
    const computedStyle = window.getComputedStyle(element);
    
    console.log('📍 Grid Area Properties:', {
      gridArea: computedStyle.gridArea,
      gridRow: computedStyle.gridRow,
      gridColumn: computedStyle.gridColumn,
      gridRowStart: computedStyle.gridRowStart,
      gridRowEnd: computedStyle.gridRowEnd,
      gridColumnStart: computedStyle.gridColumnStart,
      gridColumnEnd: computedStyle.gridColumnEnd
    });
    
    console.log('📏 Element Dimensions:', {
      width: element.offsetWidth,
      height: element.offsetHeight,
      clientWidth: element.clientWidth,
      clientHeight: element.clientHeight
    });
    
    console.log('🎨 Inline Styles:', {
      gridArea: element.style.gridArea,
      inlineStylesCount: element.style.length
    });
    
    console.log('✅ Validation:', {
      expectedGridArea,
      actualGridArea: computedStyle.gridArea,
      inlineGridArea: element.style.gridArea,
      matches: computedStyle.gridArea === expectedGridArea || element.style.gridArea === expectedGridArea
    });
    
    // Check for CSS overrides
    const allRules = this.getCSSRulesForElement(element);
    const gridAreaRules = allRules.filter(rule => 
      rule.style && (rule.style.gridArea || rule.style.gridRow || rule.style.gridColumn)
    );
    
    if (gridAreaRules.length > 0) {
      console.warn('⚠️ CSS Rules affecting grid positioning:', gridAreaRules);
    }
    
    console.groupEnd();
  }

  /**
   * Get all CSS rules that apply to an element
   */
  private static getCSSRulesForElement(element: HTMLElement): any[] {
    const rules: any[] = [];
    const sheets = Array.from(document.styleSheets);
    
    for (const sheet of sheets) {
      try {
        const cssRules = Array.from(sheet.cssRules || sheet.rules || []);
        for (const rule of cssRules) {
          if (rule.type === CSSRule.STYLE_RULE) {
            try {
              if (element.matches((rule as CSSStyleRule).selectorText)) {
                rules.push(rule);
              }
            } catch (e) {
              // Ignore invalid selectors
            }
          }
        }
      } catch (e) {
        // Ignore cross-origin stylesheets
      }
    }
    
    return rules;
  }

  /**
   * Debug entire grid layout
   */
  static debugGridLayout(containerSelector: string): void {
    console.group('🔍 Complete Grid Layout Debug');
    
    const container = document.querySelector(containerSelector) as HTMLElement;
    if (!container) {
      console.error(`❌ Container not found: ${containerSelector}`);
      console.groupEnd();
      return;
    }
    
    console.log('🏗️ Container found:', container);
    this.debugGridContainer(container);
    
    const gridFields = container.querySelectorAll('.grid-field');
    console.log(`📦 Found ${gridFields.length} grid fields`);
    
    gridFields.forEach((field, index) => {
      const element = field as HTMLElement;
      const expectedGridArea = element.style.gridArea || 'auto';
      console.log(`\n--- Field ${index + 1} ---`);
      this.debugGridField(element, expectedGridArea);
    });
    
    console.groupEnd();
  }

  /**
   * Check for responsive CSS overrides
   */
  static checkResponsiveOverrides(): void {
    console.group('📱 Responsive CSS Override Check');
    
    const mediaQueries = [
      '(max-width: 768px)',
      '(max-width: 480px)',
      '(min-width: 769px)',
      '(min-width: 1200px)'
    ];
    
    mediaQueries.forEach(query => {
      const matches = window.matchMedia(query).matches;
      console.log(`${matches ? '✅' : '❌'} ${query}: ${matches}`);
    });
    
    // Check for the specific override that resets grid-area
    const mobileQuery = window.matchMedia('(max-width: 768px)');
    if (mobileQuery.matches) {
      console.warn('⚠️ Mobile responsive override is active!');
      console.warn('This will reset grid-area to "auto" and collapse to single column');
      console.log('CSS rule: .form-grid-positioned .grid-field { grid-area: auto !important; }');
    }
    
    console.groupEnd();
  }

  /**
   * Test grid area calculation with sample data
   */
  static testGridCalculations(): void {
    console.group('🧪 Grid Calculation Tests');
    
    const testCases = [
      { fieldName: 'normal', row: 1, column: 1, rowSize: 1, colSize: 1, expected: '1 / 1 / 2 / 2' },
      { fieldName: 'rowSpan2', row: 1, column: 2, rowSize: 2, colSize: 1, expected: '1 / 2 / 3 / 3' },
      { fieldName: 'colSpan2', row: 2, column: 1, rowSize: 1, colSize: 2, expected: '2 / 1 / 3 / 3' },
      { fieldName: 'span2x2', row: 3, column: 1, rowSize: 2, colSize: 2, expected: '3 / 1 / 5 / 3' }
    ];
    
    testCases.forEach(testCase => {
      const result = this.debugGridArea(testCase);
      const passed = result === testCase.expected;
      console.log(`${passed ? '✅' : '❌'} ${testCase.fieldName}: ${passed ? 'PASS' : 'FAIL'}`);
      if (!passed) {
        console.error(`Expected: ${testCase.expected}, Got: ${result}`);
      }
    });
    
    console.groupEnd();
  }
}

// Make it available globally for browser console debugging
(window as any).GridDebugHelper = GridDebugHelper;

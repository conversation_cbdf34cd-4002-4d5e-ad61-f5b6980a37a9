<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RowSize Debug Test</title>
    <style>
        .test-grid {
            display: grid;
            gap: 16px;
            grid-auto-rows: minmax(auto, auto);
            grid-template-columns: 1fr 1fr;
            border: 2px solid #000;
            padding: 20px;
            margin: 20px;
            background-color: #f0f0f0;
        }

        .grid-field {
            border: 2px solid #007bff;
            padding: 10px;
            background-color: white;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .field-1 {
            grid-area: 1 / 1 / 2 / 2; /* row 1, col 1, rowSize 1, colSize 1 */
            background-color: #ffcccc;
        }

        .field-2 {
            grid-area: 1 / 2 / 3 / 3; /* row 1, col 2, rowSize 2, colSize 1 */
            background-color: #ccffcc;
        }

        .field-3 {
            grid-area: 2 / 1 / 3 / 2; /* row 2, col 1, rowSize 1, colSize 1 */
            background-color: #ccccff;
        }

        .debug-info {
            margin: 20px;
            padding: 15px;
            background-color: #ffffcc;
            border: 1px solid #ccc;
            font-family: monospace;
        }

        .test-section {
            margin: 30px 0;
            border: 1px solid #ddd;
            padding: 20px;
        }

        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }

        .calculation {
            background-color: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>RowSize Debug Test</h1>

    <div class="test-section">
        <h2>Test 1: Basic Grid with rowSize = 2</h2>
        <div class="debug-info">
            <strong>Field Configuration:</strong><br>
            Field 1: row=1, column=1, rowSize=1, colSize=1 → grid-area: "1 / 1 / 2 / 2"<br>
            Field 2: row=1, column=2, rowSize=2, colSize=1 → grid-area: "1 / 2 / 3 / 3" (spans 2 rows)<br>
            Field 3: row=2, column=1, rowSize=1, colSize=1 → grid-area: "2 / 1 / 3 / 2"<br>
        </div>

        <div class="calculation">
            <strong>Grid Area Calculation for rowSize=2:</strong><br>
            rowStart = 1<br>
            rowEnd = 1 + 2 = 3<br>
            colStart = 2<br>
            colEnd = 2 + 1 = 3<br>
            Result: "1 / 2 / 3 / 3"
        </div>

        <div class="test-grid">
            <div class="grid-field field-1">Field 1<br>(1x1)</div>
            <div class="grid-field field-2">Field 2<br>(rowSize=2)<br>Should span 2 rows</div>
            <div class="grid-field field-3">Field 3<br>(1x1)</div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Dynamic Grid Area Test</h2>
        <div id="dynamic-test" class="test-grid">
            <!-- Fields will be added dynamically -->
        </div>
        <button onclick="testDynamicGrid()">Test Dynamic Grid Areas</button>
        <div id="dynamic-results" class="debug-info" style="display: none;"></div>
    </div>

    <script>
        // Simulate the calculateGridArea function from DynamicFormComponent
        function calculateGridArea(field) {
            const row = field.row || 1;
            const column = field.column || 1;
            const rowSize = field.rowSize || 1;
            const colSize = field.colSize || 1;

            const rowStart = row;
            const rowEnd = row + rowSize;
            const colStart = column;
            const colEnd = column + colSize;

            return `${rowStart} / ${colStart} / ${rowEnd} / ${colEnd}`;
        }

        function testDynamicGrid() {
            const testFields = [
                { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 },
                { fieldName: 'field2', row: 1, column: 2, rowSize: 2, colSize: 1 },
                { fieldName: 'field3', row: 2, column: 1, rowSize: 1, colSize: 1 },
                { fieldName: 'field4', row: 3, column: 1, rowSize: 1, colSize: 2 }
            ];

            const container = document.getElementById('dynamic-test');
            const results = document.getElementById('dynamic-results');
            
            // Clear previous content
            container.innerHTML = '';
            
            let resultsHTML = '<strong>Dynamic Grid Test Results:</strong><br><br>';

            testFields.forEach((field, index) => {
                const gridArea = calculateGridArea(field);
                
                // Create field element
                const fieldElement = document.createElement('div');
                fieldElement.className = 'grid-field';
                fieldElement.style.gridArea = gridArea;
                fieldElement.style.backgroundColor = `hsl(${index * 60}, 70%, 80%)`;
                fieldElement.innerHTML = `${field.fieldName}<br>row=${field.row}, col=${field.column}<br>rowSize=${field.rowSize}, colSize=${field.colSize}<br>grid-area: ${gridArea}`;
                
                container.appendChild(fieldElement);
                
                resultsHTML += `${field.fieldName}: ${gridArea}<br>`;
            });

            results.innerHTML = resultsHTML;
            results.style.display = 'block';
        }

        // Test on page load
        window.onload = function() {
            console.log('Testing grid area calculations:');
            
            const testField = { row: 1, column: 2, rowSize: 2, colSize: 1 };
            const result = calculateGridArea(testField);
            
            console.log('Test field:', testField);
            console.log('Calculated grid-area:', result);
            console.log('Expected: "1 / 2 / 3 / 3"');
            console.log('Match:', result === "1 / 2 / 3 / 3");
        };
    </script>
</body>
</html>
